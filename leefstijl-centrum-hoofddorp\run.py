#!/usr/bin/env python3
"""
Leefstijl Centrum Hoofddorp - Website Runner
Simple script to start the Flask application
"""

from app import app
import os

if __name__ == '__main__':
    # Set environment variables for development
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    print("=" * 60)
    print("🏥 Leefstijl Centrum Hoofddorp - Website")
    print("=" * 60)
    print("🌐 Starting Flask application...")
    print("📍 URL: http://localhost:5000")
    print("=" * 60)
    print("\n📋 Demo Login Credentials:")
    print("👨‍⚕️ Medewerker: medewerker / fysio123")
    print("👤 Cliënt: jan.doe / willekeurig")
    print("=" * 60)
    print("\n🚀 Application starting...\n")
    
    # Run the Flask application
    app.run(
        debug=True,
        host='0.0.0.0',
        port=5000,
        use_reloader=True
    )
