@echo off
echo ============================================================
echo    Leefstijl Centrum Hoofddorp - Website Starter
echo ============================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is niet geïnstalleerd!
    echo    Download Python van: https://python.org
    pause
    exit /b 1
)

echo ✅ Python gevonden
echo.

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip is niet beschikbaar!
    pause
    exit /b 1
)

echo ✅ pip gevonden
echo.

REM Install requirements if they don't exist
echo 📦 Installeren van dependencies...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ Fout bij installeren van dependencies!
    pause
    exit /b 1
)

echo ✅ Dependencies geïnstalleerd
echo.

REM Create data directory if it doesn't exist
if not exist "data" (
    echo 📁 Aanmaken data directory...
    mkdir data
)

echo ============================================================
echo 🚀 Starten van de website...
echo ============================================================
echo.
echo 🌐 Website URL: http://localhost:5000
echo.
echo 📋 Demo Login Gegevens:
echo    👨‍⚕️ Medewerker: medewerker / fysio123
echo    👤 Cliënt: jan.doe / willekeurig
echo.
echo ⚠️  Druk Ctrl+C om te stoppen
echo ============================================================
echo.

REM Start the application
python run.py

pause
