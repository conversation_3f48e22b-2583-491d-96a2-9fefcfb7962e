{% extends "base.html" %}

{% block title %}Agenda - Leefstijl Centrum Hoofddorp{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-calendar-alt me-2 text-primary"></i>
        Agenda Overzicht
    </h2>
    <div>
        <button class="btn btn-success" onclick="alert('Functie in ontwikkeling')">
            <i class="fas fa-plus me-2"></i>Nieuwe Afspraak
        </button>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title text-primary">
                    <i class="fas fa-filter me-2"></i>Filters
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Datum</label>
                        <input type="date" class="form-control" id="filterDate">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Behandelaar</label>
                        <select class="form-select" id="filterTherapist">
                            <option value="">Alle behandelaars</option>
                            <option value="Dr. Sarah van der Berg">Dr. Sarah van der Berg</option>
                            <option value="Mark Jansen">Mark Jansen</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title text-info">
                    <i class="fas fa-chart-bar me-2"></i>Vandaag's Statistieken
                </h6>
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-primary">8</h4>
                        <small>Afspraken</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-success">6</h4>
                        <small>Voltooid</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning">2</h4>
                        <small>Gepland</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-calendar-check me-2"></i>
            Afspraken Planning
        </h5>
    </div>
    <div class="card-body">
        {% if appointments %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><i class="fas fa-calendar me-1"></i>Datum</th>
                            <th><i class="fas fa-clock me-1"></i>Tijd</th>
                            <th><i class="fas fa-user me-1"></i>Cliënt</th>
                            <th><i class="fas fa-user-md me-1"></i>Behandelaar</th>
                            <th><i class="fas fa-stethoscope me-1"></i>Type</th>
                            <th><i class="fas fa-info-circle me-1"></i>Status</th>
                            <th><i class="fas fa-cogs me-1"></i>Acties</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for appointment in appointments %}
                        <tr>
                            <td>{{ appointment.date }}</td>
                            <td>{{ appointment.time }}</td>
                            <td>
                                <strong>{{ appointment.client_name }}</strong>
                                {% if appointment.client_phone %}
                                    <br><small class="text-muted">{{ appointment.client_phone }}</small>
                                {% endif %}
                            </td>
                            <td>{{ appointment.therapist }}</td>
                            <td>
                                <span class="badge bg-info">{{ appointment.type }}</span>
                            </td>
                            <td>
                                {% if appointment.status == 'confirmed' %}
                                    <span class="badge bg-success">Bevestigd</span>
                                {% elif appointment.status == 'pending' %}
                                    <span class="badge bg-warning">In afwachting</span>
                                {% elif appointment.status == 'completed' %}
                                    <span class="badge bg-primary">Voltooid</span>
                                {% elif appointment.status == 'cancelled' %}
                                    <span class="badge bg-danger">Geannuleerd</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ appointment.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="alert('Functie in ontwikkeling')" title="Bewerken">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="alert('Functie in ontwikkeling')" title="Bevestigen">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="alert('Functie in ontwikkeling')" title="Annuleren">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times text-muted mb-3" style="font-size: 4rem;"></i>
                <h5 class="text-muted">Geen afspraken gevonden</h5>
                <p class="text-muted">Er zijn momenteel geen afspraken ingepland.</p>
                <button class="btn btn-primary" onclick="alert('Functie in ontwikkeling')">
                    <i class="fas fa-plus me-2"></i>Eerste Afspraak Maken
                </button>
            </div>
        {% endif %}
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Beschikbare Tijdsloten Vandaag
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6 class="text-success">Beschikbaar</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-circle text-success me-2"></i>11:00 - 12:00</li>
                            <li><i class="fas fa-circle text-success me-2"></i>15:30 - 16:30</li>
                            <li><i class="fas fa-circle text-success me-2"></i>17:00 - 18:00</li>
                        </ul>
                    </div>
                    <div class="col-6">
                        <h6 class="text-danger">Bezet</h6>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-circle text-danger me-2"></i>09:00 - 10:00</li>
                            <li><i class="fas fa-circle text-danger me-2"></i>10:30 - 11:30</li>
                            <li><i class="fas fa-circle text-danger me-2"></i>14:00 - 15:00</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Aandachtspunten
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-bell text-warning me-2"></i>
                        <strong>14:00:</strong> Nieuwe cliënt - extra tijd inplannen
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-phone text-info me-2"></i>
                        <strong>16:00:</strong> Bevestiging afspraak nog niet ontvangen
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-notes-medical text-success me-2"></i>
                        <strong>17:00:</strong> Follow-up behandeling - vorige notities bekijken
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
