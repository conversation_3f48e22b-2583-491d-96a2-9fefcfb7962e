{% extends "base.html" %}

{% block title %}Cliënt Informatie - Leefstijl Centrum Hoofddorp{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-users me-2 text-primary"></i>
        Cliënt Informatie
    </h2>
    <div>
        <a href="{{ url_for('client_registration') }}" class="btn btn-success">
            <i class="fas fa-user-plus me-2"></i>Nieuwe Cliënt
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title text-primary">
                    <i class="fas fa-search me-2"></i>Zoek Cliënt
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="searchName" placeholder="Zoek op naam...">
                    </div>
                    <div class="col-md-3">
                        <input type="email" class="form-control" id="searchEmail" placeholder="Email...">
                    </div>
                    <div class="col-md-3">
                        <input type="tel" class="form-control" id="searchPhone" placeholder="Telefoon...">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary w-100" onclick="searchClients()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="text-primary">Totaal Cliënten</h6>
                <h3 class="text-primary">{{ clients|length }}</h3>
                <small class="text-muted">Geregistreerd</small>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Cliënten Overzicht
        </h5>
    </div>
    <div class="card-body">
        {% if clients %}
            <div class="table-responsive">
                <table class="table table-hover" id="clientsTable">
                    <thead>
                        <tr>
                            <th><i class="fas fa-user me-1"></i>Naam</th>
                            <th><i class="fas fa-envelope me-1"></i>Email</th>
                            <th><i class="fas fa-phone me-1"></i>Telefoon</th>
                            <th><i class="fas fa-calendar me-1"></i>Registratie</th>
                            <th><i class="fas fa-notes-medical me-1"></i>Status</th>
                            <th><i class="fas fa-cogs me-1"></i>Acties</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients %}
                        <tr>
                            <td>
                                <strong>{{ client.name }}</strong>
                                {% if client.birth_date %}
                                    <br><small class="text-muted">Geboren: {{ client.birth_date }}</small>
                                {% endif %}
                            </td>
                            <td>{{ client.email }}</td>
                            <td>{{ client.phone }}</td>
                            <td>
                                {% if client.registration_date %}
                                    {{ client.registration_date[:10] }}
                                {% else %}
                                    <span class="text-muted">Onbekend</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-success">Actief</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewClient({{ client.id }})" title="Bekijken">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="editClient({{ client.id }})" title="Bewerken">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="scheduleAppointment({{ client.id }})" title="Afspraak">
                                        <i class="fas fa-calendar-plus"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users text-muted mb-3" style="font-size: 4rem;"></i>
                <h5 class="text-muted">Geen cliënten gevonden</h5>
                <p class="text-muted">Er zijn nog geen cliënten geregistreerd in het systeem.</p>
                <a href="{{ url_for('client_registration') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>Eerste Cliënt Registreren
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Client Detail Modal -->
<div class="modal fade" id="clientDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>Cliënt Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="clientDetailContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Sluiten</button>
                <button type="button" class="btn btn-primary" onclick="editCurrentClient()">
                    <i class="fas fa-edit me-2"></i>Bewerken
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function searchClients() {
    const name = document.getElementById('searchName').value.toLowerCase();
    const email = document.getElementById('searchEmail').value.toLowerCase();
    const phone = document.getElementById('searchPhone').value.toLowerCase();
    
    const table = document.getElementById('clientsTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const nameCell = row.cells[0].textContent.toLowerCase();
        const emailCell = row.cells[1].textContent.toLowerCase();
        const phoneCell = row.cells[2].textContent.toLowerCase();
        
        const matchName = name === '' || nameCell.includes(name);
        const matchEmail = email === '' || emailCell.includes(email);
        const matchPhone = phone === '' || phoneCell.includes(phone);
        
        if (matchName && matchEmail && matchPhone) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }
}

function viewClient(clientId) {
    // In a real application, this would fetch client details from the server
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">Persoonlijke Gegevens</h6>
                <p><strong>Naam:</strong> Jan Bakker</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Telefoon:</strong> 06-12345678</p>
                <p><strong>Geboortedatum:</strong> 15-03-1985</p>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary">Medische Informatie</h6>
                <p><strong>Klachten:</strong> Rugpijn, nekklachten</p>
                <p><strong>Behandeling:</strong> Fysiotherapie</p>
                <p><strong>Status:</strong> <span class="badge bg-success">Actief</span></p>
            </div>
        </div>
        <hr>
        <h6 class="text-primary">Recente Afspraken</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Datum</th>
                        <th>Tijd</th>
                        <th>Type</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2024-01-15</td>
                        <td>10:00</td>
                        <td>Fysiotherapie</td>
                        <td><span class="badge bg-success">Voltooid</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById('clientDetailContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('clientDetailModal')).show();
}

function editClient(clientId) {
    alert('Bewerk functie in ontwikkeling voor cliënt ID: ' + clientId);
}

function scheduleAppointment(clientId) {
    alert('Afspraak inplannen functie in ontwikkeling voor cliënt ID: ' + clientId);
}

function editCurrentClient() {
    alert('Bewerk functie in ontwikkeling');
}
</script>
{% endblock %}
