<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Leefstijl Centrum Hoofddorp{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">
                <i class="fas fa-heart-pulse me-2"></i>
                Leefstijl Centrum Hoofddorp
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('home') }}">Home</a>
                    </li>
                    {% if session.user_type == 'employee' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('employee_dashboard') }}">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('agenda') }}">Agenda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('client_registration') }}">Cliënt Registratie</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('client_info') }}">Cliënt Informatie</a>
                    </li>
                    {% elif session.user_type == 'client' %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('client_app') }}">Mijn Agenda</a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    {% if session.username %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ session.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>Uitloggen
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">
                            <i class="fas fa-sign-in-alt me-1"></i>Inloggen
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <main class="container mt-4">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Leefstijl Centrum Hoofddorp</h5>
                    <p class="text-muted">
                        Uw partner voor een gezonde leefstijl<br>
                        Fysiotherapie • Personal Training • Leefstijlcoaching
                    </p>
                </div>
                <div class="col-md-6">
                    <h5>Contact</h5>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt me-2"></i>Hoofddorp, Nederland<br>
                        <i class="fas fa-phone me-2"></i>023-1234567<br>
                        <i class="fas fa-envelope me-2"></i><EMAIL>
                    </p>
                </div>
            </div>
            <hr>
            <div class="text-center text-muted">
                <small>&copy; 2024 Leefstijl Centrum Hoofddorp. Alle rechten voorbehouden.</small>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
