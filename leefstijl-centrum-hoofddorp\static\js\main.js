// Main JavaScript for Leefstijl Centrum Hoofddorp

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Form validation
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Search functionality
    initializeSearch();
    
    // Date/time utilities
    initializeDateTimeUtils();
    
    // Navigation active state
    setActiveNavigation();
});

// Search functionality
function initializeSearch() {
    const searchInputs = document.querySelectorAll('[data-search-target]');
    
    searchInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            const target = document.querySelector(input.dataset.searchTarget);
            const searchTerm = input.value.toLowerCase();
            
            if (target) {
                const rows = target.querySelectorAll('tbody tr');
                rows.forEach(function(row) {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(searchTerm) ? '' : 'none';
                });
            }
        });
    });
}

// Date and time utilities
function initializeDateTimeUtils() {
    // Format dates in Dutch format
    const dateElements = document.querySelectorAll('[data-date]');
    dateElements.forEach(function(element) {
        const date = new Date(element.dataset.date);
        element.textContent = formatDateDutch(date);
    });

    // Set minimum date for date inputs to today
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];
    dateInputs.forEach(function(input) {
        if (!input.hasAttribute('data-allow-past')) {
            input.min = today;
        }
    });
}

// Format date in Dutch format
function formatDateDutch(date) {
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        locale: 'nl-NL'
    };
    return date.toLocaleDateString('nl-NL', options);
}

// Format time in Dutch format
function formatTimeDutch(date) {
    return date.toLocaleTimeString('nl-NL', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
}

// Set active navigation item
function setActiveNavigation() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(function(link) {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// Appointment management functions
function scheduleAppointment(clientId) {
    // This would typically open a modal or redirect to appointment scheduling
    showNotification('Afspraak inplannen functie wordt geladen...', 'info');
    
    // Simulate loading
    setTimeout(function() {
        showNotification('Afspraak inplannen functie is nog in ontwikkeling', 'warning');
    }, 1000);
}

function confirmAppointment(appointmentId) {
    if (confirm('Weet u zeker dat u deze afspraak wilt bevestigen?')) {
        // Simulate API call
        showLoadingState(true);
        
        setTimeout(function() {
            showLoadingState(false);
            showNotification('Afspraak succesvol bevestigd!', 'success');
            
            // Update UI
            const statusElement = document.querySelector(`[data-appointment-id="${appointmentId}"] .status`);
            if (statusElement) {
                statusElement.innerHTML = '<span class="badge bg-success">Bevestigd</span>';
            }
        }, 1000);
    }
}

function cancelAppointment(appointmentId) {
    if (confirm('Weet u zeker dat u deze afspraak wilt annuleren?')) {
        // Simulate API call
        showLoadingState(true);
        
        setTimeout(function() {
            showLoadingState(false);
            showNotification('Afspraak geannuleerd', 'info');
            
            // Update UI
            const statusElement = document.querySelector(`[data-appointment-id="${appointmentId}"] .status`);
            if (statusElement) {
                statusElement.innerHTML = '<span class="badge bg-danger">Geannuleerd</span>';
            }
        }, 1000);
    }
}

// Client management functions
function viewClientDetails(clientId) {
    // This would typically fetch client data from the server
    showLoadingState(true);
    
    setTimeout(function() {
        showLoadingState(false);
        // The actual implementation would populate a modal with client data
        showNotification('Cliënt details laden...', 'info');
    }, 500);
}

function editClient(clientId) {
    showNotification('Bewerk functie wordt geladen...', 'info');
    // This would typically open an edit form
}

// Utility functions
function showNotification(message, type = 'info') {
    const alertClass = `alert-${type}`;
    const iconClass = getIconForType(type);
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Insert at the top of the main container
    const container = document.querySelector('main.container');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-remove after 5 seconds
        setTimeout(function() {
            const alert = container.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }
}

function getIconForType(type) {
    const icons = {
        'success': 'fas fa-check-circle',
        'info': 'fas fa-info-circle',
        'warning': 'fas fa-exclamation-triangle',
        'danger': 'fas fa-exclamation-circle'
    };
    return icons[type] || icons['info'];
}

function showLoadingState(show) {
    const body = document.body;
    if (show) {
        body.classList.add('loading');
    } else {
        body.classList.remove('loading');
    }
}

// Form helpers
function validateForm(formElement) {
    const requiredFields = formElement.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        }
    });
    
    return isValid;
}

function resetForm(formElement) {
    formElement.reset();
    formElement.classList.remove('was-validated');
    
    const fields = formElement.querySelectorAll('.form-control, .form-select');
    fields.forEach(function(field) {
        field.classList.remove('is-valid', 'is-invalid');
    });
}

// Phone number formatting (Dutch format)
function formatPhoneNumber(input) {
    let value = input.value.replace(/\D/g, '');
    
    if (value.startsWith('31')) {
        value = value.substring(2);
    }
    
    if (value.startsWith('0')) {
        value = value.substring(1);
    }
    
    if (value.length >= 9) {
        value = value.substring(0, 9);
        value = `06-${value.substring(1, 3)}-${value.substring(3, 6)}-${value.substring(6)}`;
    }
    
    input.value = value;
}

// Initialize phone number formatting
document.addEventListener('DOMContentLoaded', function() {
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('input', function() {
            formatPhoneNumber(this);
        });
    });
});

// Export functions for global use
window.LeefstijlCentrum = {
    scheduleAppointment,
    confirmAppointment,
    cancelAppointment,
    viewClientDetails,
    editClient,
    showNotification,
    showLoadingState,
    validateForm,
    resetForm,
    formatDateDutch,
    formatTimeDutch
};
