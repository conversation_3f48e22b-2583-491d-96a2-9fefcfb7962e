# Leefstijl Centrum Hoofddorp - Website

Een professionele website voor Leefstijl Centrum Hoofddorp, geb<PERSON><PERSON>d met Flask en Bootstrap. De website biedt functionaliteiten voor zowel medewerkers als cliënten van het centrum.

## Functionaliteiten

### Voor Cliënten
- **Client App**: Persoonlijke agenda met overzicht van afspraken
- Toegang tot voorgeschreven oefeningen en uitleg behandeling
- Contact informatie en openingstijden

### Voor Medewerkers (Fysiotherapeuten)
- **Medewerker Dashboard**: Overzicht van dagelijkse activiteiten
- **Agenda Beheer**: Volledige agenda met alle afspraken
- **Cliënt Registratie**: Nieuwe cliënten registreren in het systeem
- **Cliënt Informatie**: Toegang tot alle cliënt gegevens en zoekfunctionaliteit

## Website Structuur

Gebaseerd op de site map:
```
Home pagina
└── Inlog pagina
    ├── Client App (Agenda)
    │   ├── Toegang tot voorgeschreven oefeningen
    │   └── Uitleg behandeling
    └── Medewerker Dashboard
        ├── Agenda
        ├── Cliënt Registratie
        └── Toegang tot Cliënt Informatie
```

## Installatie

1. **Clone of download het project**
   ```bash
   cd leefstijl-centrum-hoofddorp
   ```

2. **Installeer Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Start de applicatie**
   ```bash
   python app.py
   ```

4. **Open in browser**
   ```
   http://localhost:5000
   ```

## Demo Inloggegevens

### Medewerker Account
- **Gebruikersnaam**: `medewerker`
- **Wachtwoord**: `fysio123`
- **Toegang tot**: Dashboard, Agenda, Cliënt Registratie, Cliënt Informatie

### Cliënt Account
- **Gebruikersnaam**: `jan.doe` (of elke andere naam)
- **Wachtwoord**: `willekeurig` (elk wachtwoord werkt)
- **Toegang tot**: Persoonlijke agenda en afspraken

## Technische Details

### Backend
- **Framework**: Flask (Python)
- **Template Engine**: Jinja2
- **Data Storage**: JSON bestanden (voor demo doeleinden)
- **Session Management**: Flask sessions

### Frontend
- **CSS Framework**: Bootstrap 5.1.3
- **Icons**: Font Awesome 6.0.0
- **JavaScript**: Vanilla JS met Bootstrap componenten
- **Responsive Design**: Mobile-first approach

### Bestandsstructuur
```
leefstijl-centrum-hoofddorp/
├── app.py                 # Hoofdapplicatie
├── requirements.txt       # Python dependencies
├── README.md             # Deze documentatie
├── data/                 # Data opslag (JSON bestanden)
├── templates/            # HTML templates
│   ├── base.html
│   ├── home.html
│   ├── login.html
│   ├── client_app.html
│   ├── employee_dashboard.html
│   ├── agenda.html
│   ├── client_registration.html
│   └── client_info.html
└── static/              # Statische bestanden
    ├── css/
    │   └── style.css
    └── js/
        └── main.js
```

## Functionaliteiten in Detail

### Home Pagina
- Welkomstbericht en overzicht van diensten
- Informatie over fysiotherapie, personal training en leefstijlcoaching
- Contact informatie en call-to-action buttons

### Inlog Systeem
- Eenvoudige authenticatie voor demo doeleinden
- Verschillende toegangsniveaus voor medewerkers en cliënten
- Session management voor gebruikersstatus

### Client App
- Persoonlijke agenda met afspraken overzicht
- Status van afspraken (bevestigd, in afwachting, etc.)
- Toegang tot behandelinformatie
- Contact mogelijkheden

### Medewerker Dashboard
- Overzicht van dagelijkse statistieken
- Snelle toegang tot belangrijke functies
- Vandaag's agenda preview
- Meldingen en herinneringen

### Agenda Beheer
- Volledig overzicht van alle afspraken
- Filter mogelijkheden op datum en behandelaar
- Status beheer van afspraken
- Beschikbare tijdsloten overzicht

### Cliënt Registratie
- Formulier voor nieuwe cliënt registratie
- Persoonlijke en medische gegevens
- Privacy en toestemming afhandeling
- Validatie en opslag van gegevens

### Cliënt Informatie
- Zoekfunctionaliteit voor cliënten
- Overzicht van alle geregistreerde cliënten
- Gedetailleerde cliënt informatie
- Acties zoals bewerken en afspraak inplannen

## Uitbreidingsmogelijkheden

- **Database integratie**: Vervang JSON opslag door MySQL/PostgreSQL
- **Email notificaties**: Automatische bevestigingen en herinneringen
- **Online afspraak boeken**: Cliënten kunnen zelf afspraken inplannen
- **Behandelplan beheer**: Digitale behandelplannen en voortgang tracking
- **Rapportage**: Uitgebreide statistieken en rapporten
- **API integratie**: Koppeling met externe systemen
- **Mobile app**: Native mobile applicatie

## Support

Voor vragen of ondersteuning:
- **Email**: <EMAIL>
- **Telefoon**: 023-1234567
- **Adres**: Hoofddorp, Nederland

## Licentie

Dit project is ontwikkeld voor Leefstijl Centrum Hoofddorp.
