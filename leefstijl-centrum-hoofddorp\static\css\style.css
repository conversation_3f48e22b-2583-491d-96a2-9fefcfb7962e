/* Custom CSS for Leefstijl Centrum Hoofddorp */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    border-radius: 15px !important;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

/* Forms */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* Tables */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 6px 10px;
    border-radius: 6px;
}

/* Timeline (for employee dashboard) */
.timeline {
    position: relative;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    padding-left: 20px;
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 8px;
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.timeline-item::after {
    content: '';
    position: absolute;
    left: 11px;
    top: 16px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #e9ecef;
}

.timeline-item:last-child::after {
    display: none;
}

.timeline-time {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 60px;
    font-size: 0.9rem;
}

.timeline-content {
    margin-left: 15px;
    flex: 1;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    border-left: 4px solid;
}

.alert-success {
    border-left-color: var(--success-color);
    background-color: rgba(25, 135, 84, 0.1);
}

.alert-info {
    border-left-color: var(--info-color);
    background-color: rgba(13, 202, 240, 0.1);
}

.alert-warning {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.alert-danger {
    border-left-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.alert-sm {
    padding: 8px 12px;
    font-size: 0.875rem;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .timeline-time {
        margin-bottom: 5px;
    }
    
    .timeline-content {
        margin-left: 0;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom utility classes */
.text-primary-light {
    color: rgba(13, 110, 253, 0.8) !important;
}

.bg-primary-light {
    background-color: rgba(13, 110, 253, 0.1) !important;
}

.border-primary-light {
    border-color: rgba(13, 110, 253, 0.3) !important;
}

.shadow-soft {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08) !important;
}

.rounded-lg {
    border-radius: 12px !important;
}

.rounded-xl {
    border-radius: 16px !important;
}

/* Print styles */
@media print {
    .navbar, .btn, .alert {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
