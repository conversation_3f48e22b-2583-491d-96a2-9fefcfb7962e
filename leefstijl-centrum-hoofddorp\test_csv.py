#!/usr/bin/env python3
"""
Test script to verify CSV loading functionality
"""

import csv
import os

def load_csv_file(filename):
    """Load data from CSV file"""
    data = []
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8', newline='') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if any(row.values()):  # Skip empty rows
                    data.append(row)
    return data

if __name__ == '__main__':
    print("Testing CSV loading...")
    
    # Test clients
    clients = load_csv_file('data/clients.csv')
    print(f"Loaded {len(clients)} clients:")
    for client in clients:
        print(f"  - {client['name']} ({client['email']})")
    
    # Test appointments
    appointments = load_csv_file('data/appointments.csv')
    print(f"\nLoaded {len(appointments)} appointments:")
    for apt in appointments:
        print(f"  - {apt['date']} {apt['time']}: {apt['client_name']} with {apt['therapist']}")
    
    # Test employees
    employees = load_csv_file('data/employees.csv')
    print(f"\nLoaded {len(employees)} employees:")
    for emp in employees:
        print(f"  - {emp['name']} ({emp['specialty']})")
    
    print("\nCSV loading test completed successfully!")
