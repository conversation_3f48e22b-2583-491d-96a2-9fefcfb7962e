from flask import Flask, render_template, request, redirect, url_for, flash, session
from datetime import datetime, timedelta
import json
import os

app = Flask(__name__)
app.secret_key = 'leefstijl_centrum_secret_key_2024'

# Data storage files
APPOINTMENTS_FILE = 'data/appointments.json'
CLIENTS_FILE = 'data/clients.json'
EMPLOYEES_FILE = 'data/employees.json'

# Ensure data directory exists
os.makedirs('data', exist_ok=True)

def load_json_file(filename):
    """Load data from JSON file"""
    if os.path.exists(filename):
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    return []

def save_json_file(filename, data):
    """Save data to JSON file"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def init_sample_data():
    """Initialize sample data if files don't exist"""
    if not os.path.exists(EMPLOYEES_FILE):
        employees = [
            {
                'id': 1,
                'name': 'Dr. <PERSON>',
                'specialty': 'Fysiotherapie',
                'email': '<EMAIL>',
                'phone': '023-1234567'
            },
            {
                'id': 2,
                'name': 'Mark Jansen',
                'specialty': 'Personal Training',
                'email': '<EMAIL>',
                'phone': '023-1234568'
            }
        ]
        save_json_file(EMPLOYEES_FILE, employees)
    
    if not os.path.exists(CLIENTS_FILE):
        save_json_file(CLIENTS_FILE, [])
    
    if not os.path.exists(APPOINTMENTS_FILE):
        save_json_file(APPOINTMENTS_FILE, [])

@app.route('/')
def home():
    """Home pagina"""
    return render_template('home.html')

@app.route('/login')
def login():
    """Inlog pagina"""
    return render_template('login.html')

@app.route('/login', methods=['POST'])
def login_post():
    """Handle login form submission"""
    username = request.form.get('username')
    password = request.form.get('password')
    
    # Simple authentication (in production, use proper authentication)
    if username == 'medewerker' and password == 'fysio123':
        session['user_type'] = 'employee'
        session['username'] = username
        return redirect(url_for('employee_dashboard'))
    elif username and password:  # Any other credentials for client
        session['user_type'] = 'client'
        session['username'] = username
        return redirect(url_for('client_app'))
    else:
        flash('Ongeldige inloggegevens')
        return redirect(url_for('login'))

@app.route('/logout')
def logout():
    """Logout"""
    session.clear()
    return redirect(url_for('home'))

@app.route('/client-app')
def client_app():
    """Client App - Agenda"""
    if 'user_type' not in session or session['user_type'] != 'client':
        return redirect(url_for('login'))
    
    appointments = load_json_file(APPOINTMENTS_FILE)
    user_appointments = [apt for apt in appointments if apt.get('client_name') == session.get('username')]
    
    return render_template('client_app.html', appointments=user_appointments)

@app.route('/employee-dashboard')
def employee_dashboard():
    """Medewerker dashboard"""
    if 'user_type' not in session or session['user_type'] != 'employee':
        return redirect(url_for('login'))
    
    return render_template('employee_dashboard.html')

@app.route('/agenda')
def agenda():
    """Agenda overzicht"""
    if 'user_type' not in session or session['user_type'] != 'employee':
        return redirect(url_for('login'))
    
    appointments = load_json_file(APPOINTMENTS_FILE)
    return render_template('agenda.html', appointments=appointments)

@app.route('/client-registration')
def client_registration():
    """Client registratie"""
    if 'user_type' not in session or session['user_type'] != 'employee':
        return redirect(url_for('login'))
    
    return render_template('client_registration.html')

@app.route('/client-registration', methods=['POST'])
def client_registration_post():
    """Handle client registration"""
    if 'user_type' not in session or session['user_type'] != 'employee':
        return redirect(url_for('login'))
    
    clients = load_json_file(CLIENTS_FILE)
    
    new_client = {
        'id': len(clients) + 1,
        'name': request.form.get('name'),
        'email': request.form.get('email'),
        'phone': request.form.get('phone'),
        'address': request.form.get('address'),
        'birth_date': request.form.get('birth_date'),
        'medical_info': request.form.get('medical_info'),
        'registration_date': datetime.now().isoformat()
    }
    
    clients.append(new_client)
    save_json_file(CLIENTS_FILE, clients)
    
    flash('Cliënt succesvol geregistreerd!')
    return redirect(url_for('client_registration'))

@app.route('/client-info')
def client_info():
    """Toegang tot cliënt informatie"""
    if 'user_type' not in session or session['user_type'] != 'employee':
        return redirect(url_for('login'))
    
    clients = load_json_file(CLIENTS_FILE)
    return render_template('client_info.html', clients=clients)

if __name__ == '__main__':
    init_sample_data()
    app.run(debug=True, host='0.0.0.0', port=5000)
